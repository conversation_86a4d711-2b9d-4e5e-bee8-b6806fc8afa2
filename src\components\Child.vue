<script lang="ts" setup name="Child">
import { ref, watchEffect } from 'vue';
import { nanoid } from 'nanoid';
import emitter from '../utils/mitt';
// 秉持数据向下流动，事件向上传递的原则
const emit = defineEmits<{
    'die': [count: number]
    'get-child': [uuid: string]
    'update-click': []
}>()

const count = ref(1);

const message = ref('');
const handleEdit = () => {
    emit('die', count.value);
}

const generateUuid = () => {
    emit('get-child', nanoid())
}

watchEffect(() => {
    emitter.emit('customEvent', message.value);
});

</script>
<template>
    <h2>子组件</h2>
    <button @click="handleEdit">添加</button>
    <button @click="generateUuid">生成UUID</button>
    <button @click="emit('update-click')">Click</button>
    <input v-model="count" type="number">
    <input v-model="message" type="text" placeholder="请输入emitter">
</template>

<style scoped></style>