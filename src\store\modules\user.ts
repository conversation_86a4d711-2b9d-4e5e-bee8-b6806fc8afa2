import { defineStore } from 'pinia'
import { reactive, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
    // 状态 (state)
    const userData = reactive({
        name: null as string | null,
        age: null as number | null,
    })
    
    // 计算属性 (getters)
    const name = computed(() => userData.name)
    
    // 方法 (actions)
    function changeName(newName: string) {
        userData.name = newName
    }
    
    function changeAge(newAge: number) {
        userData.age = newAge
    }
    
    return { 
        userData, 
        name,
        changeName, 
        changeAge 
    }
})
