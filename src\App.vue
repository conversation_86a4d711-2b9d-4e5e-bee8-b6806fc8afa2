<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from './store'
import Father from './components/Father.vue'
import emitter from './utils/mitt/index.ts';
const userStore = useUserStore()
onMounted(() => {
  userStore.changeName('张三')
  emitter.emit('customEvent', 'hellow mitt!!!');
})
</script>

<template>
  <div>
    <Father />
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
