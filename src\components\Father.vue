<script lang="ts" setup name="Father">
import { ref, onMounted, onUnmounted } from 'vue'
import emitter from '../utils/mitt/index.ts';
// 秉持数据向下流动，事件向上传递的原则
import Child from './Child.vue'
onMounted(() => {
    emitter.on('customEvent', (msg: string) => {
        message.value = msg;
    })
})

onUnmounted(() => {
    emitter.off('customEvent') 
})
const count = ref(0);
const uuid = ref("");
const message = ref("");

const handleDie = (_count: number) => {

    count.value += _count;
}

const handleGetChild = (child: string) => {
    console.log(child);
    uuid.value = child;
}

const handleUpdateClick = () => {
    console.log('die');
}
</script>
<template>
    <h1>父组件</h1>
    <div>
        <h2>父组件中的数据</h2>
        <div>{{ count }}</div>
        <div>{{ uuid }}</div>
        <div>{{ message }}</div>
    </div>
    <Child @die="handleDie" @get-child="handleGetChild" @update-click="handleUpdateClick" />

</template>

<style scoped></style>